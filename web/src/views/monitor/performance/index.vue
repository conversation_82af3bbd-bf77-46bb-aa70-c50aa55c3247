<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NBreadcrumb, NBreadcrumbItem } from 'naive-ui';
import { useRouter } from 'vue-router';
import PerformanceCharts from '../dashboard/modules/performance-charts.vue';
import ResponseTimeTrendChart from './components/response-time-trend-chart.vue';

const router = useRouter();
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const performanceChartsRef = ref();

onMounted(async () => {
  try {
    loading.value = true;
    // 加载性能监控详情数据
    if (performanceChartsRef.value?.loadPerformanceData) {
      await performanceChartsRef.value.loadPerformanceData();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载性能监控详情失败';
  } finally {
    loading.value = false;
  }
});

onUnmounted(() => {
  // 清理资源
});

function goBack() {
  router.push('/monitor/dashboard');
}
</script>

<template>
  <div class="monitor-performance">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <NBreadcrumb>
        <NBreadcrumbItem @click="goBack" class="cursor-pointer">
          <span class="text-blue-600 hover:text-blue-800">系统监控</span>
        </NBreadcrumbItem>
        <NBreadcrumbItem>性能监控</NBreadcrumbItem>
      </NBreadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">性能监控详情</h1>
        <p class="text-gray-600 mt-1">查看API性能详细分析和系统性能指标</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <NAlert type="error" :title="error" closable @close="error = null" />
    </div>

    <!-- 性能监控详情内容 -->
    <NSpin :show="loading">
      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 响应时间趋势图 -->
        <NGridItem :span="24">
          <ResponseTimeTrendChart :loading="loading" />
        </NGridItem>

        <!-- 性能图表详情 -->
        <NGridItem :span="24">
          <NCard title="API性能详细分析" class="mb-4">
            <PerformanceCharts ref="performanceChartsRef" />
          </NCard>
        </NGridItem>

        <!-- 可以在这里添加更多性能相关的详细信息 -->
        <!-- 例如：慢查询分析、API趋势图表等 -->
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.monitor-performance {
  padding: 0;
}
</style>
